import { NextRequest, NextResponse } from 'next/server';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Initialize Supabase client with service role key for server-side operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

let supabase: SupabaseClient | null = null;
if (supabaseUrl && supabaseServiceKey) {
  supabase = createClient(supabaseUrl, supabaseServiceKey, {
    db: { schema: 'public' },
  });
}

// Define attractiveness factors and their corresponding semantic keywords
const ATTRACTIVENESS_KEYWORD_MAPPING: Record<string, string[]> = {
  'skin_radiance': ['skin_radiance', 'vitamin_c_glow', 'skin_brightness'],
  'skin_elasticity': ['skin_elasticity', 'collagen', 'hydration'],
  'bone_structure': ['bone_structure', 'facial_structure'],
  'eye_vitality': ['eye_vitality', 'under_eye_care'],
  'hair_health': ['hair_health', 'hair_growth', 'hair_strength'],
  'facial_symphony': ['facial_symphony', 'skin_radiance', 'bone_structure'],
  'beard_care': ['beard_care', 'facial_hair_grooming', 'masculine_grooming'],
  'undefined': ['skin_radiance', 'vitamin_c_glow', 'skin_brightness']
};

// Extract attractiveness factors from analysis recommendations
function extractAttractivenessFactors(recommendations: string): string[] {
  const lowerText = recommendations.toLowerCase();
  const factors: string[] = [];

  // Map analysis text to attractiveness factors
  if (lowerText.includes('skin') || lowerText.includes('hydration') || lowerText.includes('texture')) {
    factors.push('skin_radiance', 'skin_elasticity');
  }

  if (lowerText.includes('bone') || lowerText.includes('structure') || lowerText.includes('facial') || lowerText.includes('jaw')) {
    factors.push('bone_structure');
  }

  if (lowerText.includes('eye') || lowerText.includes('vision') || lowerText.includes('ocular')) {
    factors.push('eye_vitality');
  }

  if (lowerText.includes('hair') || lowerText.includes('growth') || lowerText.includes('thickness')) {
    factors.push('hair_health');
  }

  // Only include beard care if specifically mentioned (indicates visible facial hair)
  if (lowerText.includes('beard') || lowerText.includes('facial hair') || lowerText.includes('mustache') || lowerText.includes('goatee')) {
    factors.push('beard_care');
  }

  if (lowerText.includes('symmetry') || lowerText.includes('proportional')) {
    factors.push('facial_symphony');
  }

  // Default factors if none matched
  if (factors.length === 0) {
    factors.push('skin_radiance', 'bone_structure', 'eye_vitality');
  }

  return factors;
}

// Get relevant product keywords based on attractiveness factors
function getRelevantKeywords(factors: string[]): string[] {
  const keywords: string[] = [];
  const keywordSet = new Set<string>();

  factors.forEach(factor => {
    const factorKeywords = ATTRACTIVENESS_KEYWORD_MAPPING[factor] || [];
    factorKeywords.forEach(keyword => keywordSet.add(keyword));
  });

  // Convert set to array
  const uniqueKeywords: string[] = [];
  keywordSet.forEach(keyword => uniqueKeywords.push(keyword));
  return uniqueKeywords;
}

export async function POST(request: NextRequest) {
  try {
    const { recommendations } = await request.json();

    if (!recommendations || typeof recommendations !== 'string') {
      return NextResponse.json(
        { error: 'Recommendations text is required' },
        { status: 400 }
      );
    }

    // Extract attractiveness factors from recommendations
    const factors = extractAttractivenessFactors(recommendations);
    const keywords = getRelevantKeywords(factors);

    // Check if Supabase is configured
    if (!supabase) {
      // Return sample products for demo purposes when database isn't configured
      const sampleProducts = [
        {
          asin: 'DEMO-001',
          title: 'La Merck Vitamin C Serum for Brightening & Anti-Aging, 1 oz',
          imageUrl: 'https://m.media-amazon.com/images/I/61-LYQGtQCL._AC_UL320_.jpg',
          price: '$29.99',
          priceBeforeDiscount: null,
          rating: null,
          reviewCount: 1248,
          affiliateUrl: `https://www.amazon.com/dp/B07WZVX3L1?tag=amzleanmed-20`,
          category: 'skin_care',
          semanticKeywords: ['skin_radiance', 'vitamin_c_glow', 'skin_brightness'],
          isDiscounted: false
        },
        {
          asin: 'DEMO-002',
          title: 'The Ordinary Hyaluronic Acid 2% + B5 Serum, 30ml',
          imageUrl: 'https://m.media-amazon.com/images/I/71ZBnxvlvCL._AC_UL320_.jpg',
          price: '$7.20',
          priceBeforeDiscount: '$9.99',
          rating: null,
          reviewCount: 1586,
          affiliateUrl: `https://www.amazon.com/dp/B01NBTJFJB?tag=amzleanmed-20`,
          category: 'skin_care',
          semanticKeywords: ['skin_elasticity', 'hydration', 'facial_contouring'],
          isDiscounted: true
        },
        {
          asin: 'DEMO-003',
          title: 'CeraVe Moisturizing Cream with Ceramides and Hyaluronic Acid, 1.7 oz',
          imageUrl: 'https://m.media-amazon.com/images/I/514DekriBLL._AC_UL320_.jpg',
          price: '$18.99',
          priceBeforeDiscount: null,
          rating: null,
          reviewCount: 2856,
          affiliateUrl: `https://www.amazon.com/dp/B07C7S9Q4F?tag=amzleanmed-20`,
          category: 'skin_care',
          semanticKeywords: ['skin_radiance', 'hydration', 'skin_firmness'],
          isDiscounted: false
        }
      ];

      return NextResponse.json({
        products: sampleProducts,
        totalSelected: 3,
        factorsUsed: factors,
        message: 'Using demo products - database not configured'
      });
    }

    // Query products that match the semantic keywords
    const { data: products, error } = await supabase
      .from('amazon_product_references')
      .select('*')
      .overlaps('semantic_keywords', keywords)
      .limit(20); // Get more products for better selection

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch product recommendations' },
        { status: 500 }
      );
    }

    // Prevent duplicates by ensuring max 1 product per category
    const selectedProducts: any[] = [];
    const usedCategories = new Set<string>();
    const shuffled = products?.sort(() => 0.5 - Math.random()) || [];

    // Prioritize beard products if beard_care is in factors
    if (factors.includes('beard_care')) {
      const beardProducts = shuffled.filter(p => p.primary_category === 'beard_care');
      if (beardProducts.length > 0) {
        selectedProducts.push(beardProducts[0]);
        usedCategories.add('beard_care');
      }
    }

    // First pass: select one product from each category (excluding already selected)
    for (const product of shuffled) {
      if (selectedProducts.length >= 3) break;

      if (!usedCategories.has(product.primary_category)) {
        selectedProducts.push(product);
        usedCategories.add(product.primary_category);
      }
    }

    // Second pass: if we still need more products, add from unused categories or different subcategories
    if (selectedProducts.length < 3) {
      for (const product of shuffled) {
        if (selectedProducts.length >= 3) break;

        // Check if this product is significantly different from already selected ones
        const isDifferent = !selectedProducts.some(selected =>
          selected.product_name.toLowerCase().includes(product.product_name.toLowerCase().split(' ')[0]) ||
          product.product_name.toLowerCase().includes(selected.product_name.toLowerCase().split(' ')[0])
        );

        if (isDifferent && !selectedProducts.find(p => p.product_id === product.product_id)) {
          selectedProducts.push(product);
        }
      }
    }

    // Transform to match expected format with affiliate links
    const curatedProducts = selectedProducts.map(product => ({
      asin: product.product_id,
      title: product.product_name,
      imageUrl: product.product_image_url,
      price: `$${(product.product_price || 0).toString()}`,
      priceBeforeDiscount: product.product_price_before_discount ? `$${(product.product_price_before_discount).toString()}` : null,
      rating: null, // We don't have rating data in our table
      reviewCount: product.review_count,
      affiliateUrl: `https://www.amazon.com/dp/${product.product_id}?tag=amzleanmed-20`,
      category: product.primary_category,
      semanticKeywords: product.semantic_keywords,
      isDiscounted: product.is_discounted
    }));

    return NextResponse.json({
      products: curatedProducts,
      totalSelected: curatedProducts.length,
      factorsUsed: factors
    });

  } catch (error) {
    console.error('Curated products error:', error);
    return NextResponse.json(
      { error: 'Failed to generate product recommendations' },
      { status: 500 }
    );
  }
}
